package com.studycards.controller;

import com.studycards.dto.*;
import com.studycards.enums.CollaboratorPermission;
import com.studycards.enums  .SubscriptionStatus;
import com.studycards.exception.*;
import com.studycards.model.Deck;
import com.studycards.model.User;
import com.studycards.service.*;
import com.studycards.util.PaginationUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/decks")
public class DeckController {

    @Autowired
    private DeckService deckService;

    @Autowired
    private UserService userService;

    @Autowired
    private CollaborationService collaborationService;

    @Autowired
    private SubscriptionService subscriptionService;

    @Autowired
    private ContentVisibilityService contentVisibilityService;

    @Autowired
    private RateLimitService rateLimitService;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private ValidationMonitoringService validationMonitoringService;

    @Autowired
    private AuditLoggingService auditLoggingService;

    /**
     * Get personalized deck recommendations for the current user
     * This is the recommended endpoint for the default view of the decks page
     *
     * @param page Page number (0-based)
     * @param size Page size
     * @return Page of personalized deck recommendations
     */
    @GetMapping("/recommended")
    public ResponseEntity<Page<DeckResponse>> getPersonalizedRecommendations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PaginationUtil.createPageable(page, size);
        Page<DeckResponse> recommendations = deckService.getPersonalizedRecommendations(pageable);
        return ResponseEntity.ok(recommendations);
    }

    @GetMapping
    public ResponseEntity<Page<DeckResponse>> getPublicDecks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String direction) {

        Sort.Direction sortDirection = direction.equalsIgnoreCase("asc") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PaginationUtil.createPageable(page, size, sortBy, sortDirection);

        Page<DeckResponse> decks = deckService.getPublicDecks(pageable);
        return ResponseEntity.ok(decks);
    }

    /**
     * Basic search for public decks (legacy endpoint)
     */
    @GetMapping("/search")
    public ResponseEntity<Page<DeckResponse>> searchDecks(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Pageable pageable = PaginationUtil.createPageable(page, size);
        Page<DeckResponse> decks = deckService.searchPublicDecks(query, pageable);
        return ResponseEntity.ok(decks);
    }

    /**
     * Advanced search with multiple criteria
     *
     * @param query Search text to find in title, description, or tags (optional)
     * @param isPublic Filter by public/private status (optional)
     * @param creatorId Filter by creator ID (optional)
     * @param tagName Filter by exact tag name (optional)
     * @param page Page number (0-based)
     * @param size Page size
     * @param sortBy Field to sort by
     * @param direction Sort direction (asc/desc)
     * @return Page of deck responses matching the criteria
     */
    @GetMapping("/advanced-search")
    public ResponseEntity<Page<DeckResponse>> advancedSearch(
            @RequestParam(required = false) String query,
            @RequestParam(required = false) Boolean isPublic,
            @RequestParam(required = false) Long creatorId,
            @RequestParam(required = false) String tagName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String direction) {

        Sort.Direction sortDirection = direction.equalsIgnoreCase("asc") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PaginationUtil.createPageable(page, size, sortBy, sortDirection);

        Page<DeckResponse> decks = deckService.advancedSearch(query, isPublic, creatorId, tagName, pageable);
        return ResponseEntity.ok(decks);
    }

    /**
     * Get popular decks using the basic algorithm (legacy endpoint)
     * This only considers the number of favorites
     */
    @GetMapping("/popular")
    public ResponseEntity<Page<DeckResponse>> getPopularDecks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        // Use native pagination since this endpoint uses native queries
        // IMPORTANT: Don't add any additional sorting since the native query handles it
        Pageable pageable = PageRequest.of(page, size);
        Page<DeckResponse> decks = deckService.getPopularDecks(pageable);
        return ResponseEntity.ok(decks);
    }

    /**
     * Get popular decks using the enhanced algorithm
     * This considers multiple factors:
     * - Number of favorites (highest weight)
     * - Number of recent study sessions
     * - Recency of the deck (newer decks get a boost)
     *
     * @param page Page number (0-based)
     * @param size Page size
     * @param timeFrame Time frame to consider for recent activity ("day", "week", "month", "year")
     * @param sortBy Field to sort by (optional)
     * @param direction Sort direction ("asc" or "desc")
     * @return Page of popular decks
     */
    @GetMapping("/popular/enhanced")
    public ResponseEntity<Page<DeckResponse>> getEnhancedPopularDecks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "month") String timeFrame,
            @RequestParam(defaultValue = "popularity") String sortBy,
            @RequestParam(defaultValue = "desc") String direction) {

        Sort.Direction sortDirection = direction.equalsIgnoreCase("asc") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable;

        // If sorting by popularity, we'll use the repository's built-in sorting
        // Use native pagination methods since this endpoint uses native queries
        // IMPORTANT: Don't add any additional sorting for popularity since the native query handles it
        if (sortBy.equalsIgnoreCase("popularity")) {
            pageable = PageRequest.of(page, size);
        } else {
            pageable = PaginationUtil.createNativePageable(page, size, sortBy, sortDirection);
        }

        Page<DeckResponse> decks = deckService.getEnhancedPopularDecks(pageable, timeFrame);
        return ResponseEntity.ok(decks);
    }

    /**
     * Get the current user's decks with optional filtering
     *
     * @param page Page number (0-based)
     * @param size Page size
     * @param sortBy Field to sort by
     * @param direction Sort direction (asc/desc)
     * @param parentId Optional parent folder ID to filter by
     * @param includeFolder Optional flag to include/exclude folders
     * @param isPublic Optional flag to filter by public/private status
     * @return Page of filtered deck responses
     */
    @GetMapping("/my-decks")
    public ResponseEntity<Page<DeckResponse>> getUserDecks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String direction,
            @RequestParam(required = false) Long parentId,
            @RequestParam(required = false) Boolean includeFolder,
            @RequestParam(required = false) Boolean isPublic) {

        Sort.Direction sortDirection = direction.equalsIgnoreCase("asc") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PaginationUtil.createPageable(page, size, sortBy, sortDirection);

        Page<DeckResponse> decks = deckService.getUserDecks(pageable, parentId, includeFolder, isPublic);
        return ResponseEntity.ok(decks);
    }



    /**
     * Get a deck by ID (basic endpoint)
     *
     * @param id The deck ID
     * @return The deck response
     */
    @GetMapping("/{id}")
    public ResponseEntity<DeckResponse> getDeckById(@PathVariable Long id) {
        // Use getDeckByIdWithDetails to eagerly load creator and avoid lazy loading issues
        Deck deck = deckService.getDeckByIdWithDetails(id);
        User currentUser = userService.getCurrentUser();

        // Use optimized mapping with context to avoid LazyInitializationException
        DeckResponse deckResponse = deckService.mapToDeckResponse(deck, currentUser);
        return ResponseEntity.ok(deckResponse);
    }

    /**
     * Get enhanced deck information including study statistics and related decks
     *
     * @param id The deck ID
     * @param includeCards Whether to include cards in the response
     * @return Enhanced deck response
     */
    @GetMapping("/{id}/enhanced")
    public ResponseEntity<EnhancedDeckResponse> getEnhancedDeckById(
            @PathVariable Long id,
            @RequestParam(defaultValue = "false") boolean includeCards) {

        try {
            EnhancedDeckResponse response = deckService.getEnhancedDeckById(id, includeCards);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            log.error("Invalid deck ID: {}", id, e);
            return ResponseEntity.badRequest().build();
        } catch (AccessDeniedException e) {
            log.error("Access denied for deck ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        } catch (Exception e) {
            log.error("Error fetching enhanced deck data for ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping
    public ResponseEntity<?> createDeck(@Valid @RequestBody DeckRequest deckRequest) {
        try {
            // Additional validation
            if (deckRequest.getTitle() == null || deckRequest.getTitle().trim().isEmpty()) {
                Map<String, String> error = Map.of("message", "Deck title is required");
                return ResponseEntity.badRequest().body(error);
            }

            DeckResponse newDeck = deckService.createDeck(deckRequest);
            return ResponseEntity.status(HttpStatus.CREATED).body(newDeck);
        } catch (IllegalArgumentException e) {
            log.error("Invalid deck creation request: {}", e.getMessage(), e);
            Map<String, String> error = Map.of("message", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        } catch (com.studycards.exception.SubscriptionLimitException e) {
            log.warn("Subscription limit exceeded for deck creation: {}", e.getMessage());
            Map<String, String> error = Map.of("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(error);
        } catch (AccessDeniedException e) {
            log.error("Access denied for deck creation: {}", e.getMessage(), e);
            Map<String, String> error = Map.of("message", "You don't have permission to create decks");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(error);
        } catch (Exception e) {
            log.error("Error creating deck: {}", e.getMessage(), e);
            Map<String, String> error = Map.of("message", "An error occurred while creating the deck. Please try again.");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    /**
     * Update a deck with optional batch card operations
     *
     * @param id The deck ID
     * @param deckRequest The deck request containing updated deck information and optional card operations
     * @return The updated deck response with cards if requested
     */
    @PutMapping("/{id}")
    public ResponseEntity<?> updateDeck(
            @PathVariable Long id,
            @Valid @RequestBody DeckRequest deckRequest,
            @RequestParam(defaultValue = "false") boolean includeCards) {

        DeckResponse updatedDeck = deckService.updateDeck(id, deckRequest);

        if (includeCards) {
            // If cards are requested, return an enhanced response with cards
            EnhancedDeckResponse enhancedResponse = deckService.getEnhancedDeckById(id, true);
            return ResponseEntity.ok(enhancedResponse);
        } else {
            // Otherwise, return the basic deck response
            return ResponseEntity.ok(updatedDeck);
        }
    }

    /**
     * Soft delete a deck
     *
     * @param id The deck ID
     * @return Success message
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteDeck(@PathVariable Long id) {
        deckService.deleteDeck(id);
        return ResponseEntity.ok("Deck moved to trash");
    }

    /**
     * Permanently delete a deck
     *
     * @param id The deck ID
     * @return Success message
     */
    @DeleteMapping("/{id}/permanent")
    public ResponseEntity<?> permanentlyDeleteDeck(@PathVariable Long id) {
        deckService.permanentlyDeleteDeck(id);
        return ResponseEntity.ok("Deck permanently deleted");
    }

    /**
     * Bulk permanently delete multiple decks
     *
     * @param deckIds List of deck IDs to permanently delete
     * @return Success message with count
     */
    @DeleteMapping("/bulk/permanent")
    public ResponseEntity<?> bulkPermanentlyDeleteDecks(@RequestBody List<Long> deckIds) {
        try {
            if (deckIds == null || deckIds.isEmpty()) {
                return ResponseEntity.badRequest().body("No deck IDs provided");
            }

            int deletedCount = deckService.bulkPermanentlyDeleteDecks(deckIds);
            return ResponseEntity.ok(Map.of(
                "message", deletedCount + " deck(s) permanently deleted",
                "deletedCount", deletedCount
            ));
        } catch (Exception e) {
            log.error("Error during bulk permanent deletion", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("message", "Failed to delete some decks"));
        }
    }

    /**
     * Restore a deleted deck
     *
     * @param id The deck ID
     * @return The restored deck
     */
    @PostMapping("/{id}/restore")
    public ResponseEntity<DeckResponse> restoreDeck(@PathVariable Long id) {
        DeckResponse restoredDeck = deckService.restoreDeck(id);
        return ResponseEntity.ok(restoredDeck);
    }

    /**
     * Bulk restore multiple deleted decks
     *
     * @param deckIds List of deck IDs to restore
     * @return Success message with count
     */
    @PostMapping("/bulk/restore")
    public ResponseEntity<?> bulkRestoreDecks(@RequestBody List<Long> deckIds) {
        try {
            if (deckIds == null || deckIds.isEmpty()) {
                return ResponseEntity.badRequest().body("No deck IDs provided");
            }

            int restoredCount = deckService.bulkRestoreDecks(deckIds);
            return ResponseEntity.ok(Map.of(
                "message", restoredCount + " deck(s) restored successfully",
                "restoredCount", restoredCount
            ));
        } catch (Exception e) {
            log.error("Error during bulk restore", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("message", "Failed to restore some decks"));
        }
    }

    /**
     * Get recently deleted decks
     *
     * @param page Page number (0-based)
     * @param size Page size
     * @return Page of recently deleted decks
     */
    @GetMapping("/trash")
    public ResponseEntity<Page<DeckResponse>> getDeletedDecks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Sort sort = Sort.by(Sort.Direction.DESC, "deletedAt");
        Pageable pageable = PaginationUtil.createPageable(page, size, sort);
        Page<DeckResponse> deletedDecks = deckService.getDeletedDecks(pageable);
        return ResponseEntity.ok(deletedDecks);
    }

    /**
     * Toggle favorite status for a deck
     *
     * @param id The deck ID
     * @return FavoriteResponse containing updated favorite status and count
     */
    @PostMapping("/{id}/favorite")
    public ResponseEntity<FavoriteResponse> toggleFavoriteDeck(@PathVariable Long id) {
        FavoriteResponse response = userService.toggleFavoriteDeck(id);
        return ResponseEntity.ok(response);
    }

    /**
     * Get user's favorite decks with pagination
     *
     * @param pageable Pagination information
     * @return Page of favorite decks
     */
    @GetMapping("/favorites")
    public ResponseEntity<Page<DeckResponse>> getUserFavorites(Pageable pageable) {
        Page<DeckResponse> favorites = deckService.getUserFavoriteDecks(pageable);
        return ResponseEntity.ok(favorites);
    }

    /**
     * Search within user's favorite decks
     *
     * @param query Search query
     * @param pageable Pagination information
     * @return Page of matching favorite decks
     */
    @GetMapping("/favorites/search")
    public ResponseEntity<Page<DeckResponse>> searchUserFavorites(
            @RequestParam String query,
            Pageable pageable) {
        Page<DeckResponse> results = deckService.searchUserFavorites(query, pageable);
        return ResponseEntity.ok(results);
    }
    /**
     * Get the user's deck hierarchy
     *
     * @param paginated Whether to use pagination
     * @param page The page number (for paginated requests)
     * @param size The page size (for paginated requests)
     * @return Deck hierarchy response
     */
    @GetMapping("/hierarchy")
    public ResponseEntity<?> getUserDeckHierarchy(
            @RequestParam(required = false, defaultValue = "false") boolean paginated,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        // Temporarily return an empty response to disable folder hierarchy feature
        DeckHierarchyResponse response = DeckHierarchyResponse.builder()
                .items(new ArrayList<>())
                .totalItems(0)
                .totalFolders(0)
                .totalDecks(0)
                .maxDepth(0)
                .paginated(false)
                .build();

        return ResponseEntity.ok(response);

        /* Temporarily disabled folder hierarchy feature
        if (paginated) {
            Pageable pageable = PaginationUtil.createPageable(page, size);
            DeckHierarchyResponse hierarchy = deckService.getUserDeckHierarchyPaginated(pageable);
            return ResponseEntity.ok(hierarchy);
        } else {
            List<DeckResponse> hierarchy = deckService.getUserDeckHierarchy();

            // Create a response with metadata but without pagination
            DeckHierarchyResponse response = DeckHierarchyResponse.builder()
                    .items(hierarchy)
                    .totalItems(hierarchy.size())
                    .totalFolders((int) hierarchy.stream().filter(DeckResponse::isFolder).count())
                    .totalDecks((int) hierarchy.stream().filter(d -> !d.isFolder()).count())
                    .maxDepth(1) // This is calculated properly in the service for paginated requests
                    .paginated(false)
                    .build();

            return ResponseEntity.ok(response);
        }
        */
    }

    /**
     * Get child decks for a specific folder
     *
     * @param folderId The folder ID
     * @return List of child deck responses
     */
    @GetMapping("/folders/{folderId}/children")
    public ResponseEntity<List<DeckResponse>> getChildDecks(@PathVariable Long folderId) {
        // Temporarily return an empty list to disable folder hierarchy feature
        return ResponseEntity.ok(new ArrayList<>());

        /* Temporarily disabled folder hierarchy feature
        List<DeckResponse> children = deckService.getChildDecks(folderId);
        return ResponseEntity.ok(children);
        */
    }

    @PostMapping("/folders")
    public ResponseEntity<DeckResponse> createFolder(
            @RequestParam String folderName,
            @RequestParam(required = false) Long parentDeckId) {

        // Temporarily return an error response to disable folder creation
        return ResponseEntity.badRequest().body(
            DeckResponse.builder()
                .title("Folder creation temporarily disabled")
                .build()
        );

        /* Temporarily disabled folder hierarchy feature
        DeckResponse folder = deckService.createFolder(folderName, parentDeckId);
        return ResponseEntity.ok(folder);
        */
    }

    @PutMapping("/{deckId}/move")
    public ResponseEntity<?> moveDeckToFolder(
            @PathVariable Long deckId,
            @RequestParam(required = false) Long folderDeckId) {

        // Temporarily return an error response to disable moving decks to folders
        return ResponseEntity.badRequest().body("Folder operations temporarily disabled");

        /* Temporarily disabled folder hierarchy feature
        deckService.moveDeckToFolder(deckId, folderDeckId);
        return ResponseEntity.ok("Deck moved successfully");
        */
    }

    /**
     * Validate deck access with comprehensive security and business logic checks
     *
     * @param deckId The deck ID to validate access for
     * @param requiredPermission The minimum permission level required
     * @param request HTTP request for IP tracking and rate limiting
     * @return DeckAccessValidationResponse with validation results
     */
    public DeckAccessValidationResponse validateDeckAccess(
            Long deckId,
            CollaboratorPermission requiredPermission,
            HttpServletRequest request) {

        long startTime = System.currentTimeMillis();
        String clientIp = getClientIpAddress(request);

        try {
            log.debug("Starting deck access validation for deckId: {}, permission: {}, IP: {}",
                     deckId, requiredPermission, clientIp);

            // 1. Input validation and sanitization
            DeckAccessValidationResponse validationResponse = validateDeckAccessInput(deckId, requiredPermission, clientIp);
            if (!validationResponse.isValid()) {
                // Record validation failure
                validationMonitoringService.recordValidationFailure(
                    "deck_access", validationResponse.getErrorCode(), null, clientIp,
                    Map.of("deckId", deckId, "permission", requiredPermission.toString())
                );
                return validationResponse;
            }

            // 2. Get current user with comprehensive validation
            User currentUser = getCurrentUserWithValidation();
            if (currentUser == null) {
                // Record authentication failure
                auditLoggingService.logAuthenticationEvent(
                    "unknown", "deck_access_attempt", false, clientIp,
                    request.getHeader("User-Agent"),
                    Map.of("deckId", deckId, "permission", requiredPermission.toString())
                );

                validationMonitoringService.recordValidationFailure(
                    "deck_access", "AUTH_REQUIRED", null, clientIp,
                    Map.of("deckId", deckId, "permission", requiredPermission.toString())
                );

                return DeckAccessValidationResponse.builder()
                    .valid(false)
                    .errorCode("AUTH_REQUIRED")
                    .errorMessage("Authentication required")
                    .httpStatus(HttpStatus.UNAUTHORIZED)
                    .build();
            }

            // 3. Rate limiting check
            try {
                rateLimitService.checkDeckAccessLimit(currentUser.getId(), clientIp);
            } catch (RateLimitExceededException e) {
                log.warn("Rate limit exceeded for deck access - User: {}, IP: {}", currentUser.getId(), clientIp);
                return DeckAccessValidationResponse.builder()
                    .valid(false)
                    .errorCode("RATE_LIMIT_EXCEEDED")
                    .errorMessage("Too many requests. Please try again later.")
                    .httpStatus(HttpStatus.TOO_MANY_REQUESTS)
                    .retryAfterSeconds((int) e.getRetryAfterSeconds())
                    .build();
            }

            // 4. Get and validate deck existence
            Deck deck;
            try {
                deck = deckService.getDeckById(deckId);
                if (deck == null) {
                    return DeckAccessValidationResponse.builder()
                        .valid(false)
                        .errorCode("DECK_NOT_FOUND")
                        .errorMessage("Deck not found")
                        .httpStatus(HttpStatus.NOT_FOUND)
                        .build();
                }
            } catch (ResourceNotFoundException e) {
                return DeckAccessValidationResponse.builder()
                    .valid(false)
                    .errorCode("DECK_NOT_FOUND")
                    .errorMessage("Deck not found")
                    .httpStatus(HttpStatus.NOT_FOUND)
                    .build();
            }

            // 5. Check if deck is deleted
            if (deck.isDeleted()) {
                return DeckAccessValidationResponse.builder()
                    .valid(false)
                    .errorCode("DECK_DELETED")
                    .errorMessage("This deck has been deleted")
                    .httpStatus(HttpStatus.GONE)
                    .build();
            }

            // 6. Subscription status validation (MVP requirement)
            DeckAccessValidationResponse subscriptionValidation = validateSubscriptionForDeckAccess(currentUser, deck, requiredPermission);
            if (!subscriptionValidation.isValid()) {
                return subscriptionValidation;
            }

            // 7. Content visibility validation (expired users restriction)
            if (!contentVisibilityService.isContentVisible(deck.getCreator(), currentUser)) {
                return DeckAccessValidationResponse.builder()
                    .valid(false)
                    .errorCode("CONTENT_NOT_VISIBLE")
                    .errorMessage("Cannot access content from expired user")
                    .httpStatus(HttpStatus.FORBIDDEN)
                    .build();
            }

            // 8. Permission validation
            if (!collaborationService.hasPermission(currentUser, deck, requiredPermission)) {
                return DeckAccessValidationResponse.builder()
                    .valid(false)
                    .errorCode("INSUFFICIENT_PERMISSIONS")
                    .errorMessage("You do not have sufficient permissions for this operation")
                    .httpStatus(HttpStatus.FORBIDDEN)
                    .requiredPermission(requiredPermission)
                    .userPermission(collaborationService.getUserPermission(currentUser, deck))
                    .build();
            }

            // Success - log and return positive validation
            long duration = System.currentTimeMillis() - startTime;
            log.info("Deck access validation successful - User: {}, Deck: {}, Permission: {}, Duration: {}ms",
                    currentUser.getId(), deckId, requiredPermission, duration);

            // Record successful validation
            validationMonitoringService.recordValidationSuccess("deck_access", duration, currentUser.getId().toString(), clientIp);

            // Record audit log for successful access
            auditLoggingService.logAuthorizationEvent(
                currentUser.getId().toString(), "deck:" + deckId, "access", true,
                "Permission granted: " + requiredPermission, clientIp,
                Map.of("deckId", deckId, "permission", requiredPermission.toString(), "duration", duration)
            );

            // Record performance metrics if slow
            if (duration >= 1000) { // 1 second threshold
                validationMonitoringService.recordPerformanceIssue("deck_access", duration,
                    currentUser.getId().toString(), clientIp,
                    Map.of("deckId", deckId, "permission", requiredPermission.toString())
                );
            }

            return DeckAccessValidationResponse.builder()
                .valid(true)
                .deck(deck)
                .user(currentUser)
                .grantedPermission(collaborationService.getUserPermission(currentUser, deck))
                .validationDurationMs(duration)
                .build();

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Unexpected error during deck access validation - DeckId: {}, IP: {}, Duration: {}ms, Error: {}",
                     deckId, clientIp, duration, e.getMessage(), e);

            return DeckAccessValidationResponse.builder()
                .valid(false)
                .errorCode("VALIDATION_ERROR")
                .errorMessage("An error occurred while validating access")
                .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                .build();
        }
    }

    /**
     * Validate input parameters for deck access validation
     */
    private DeckAccessValidationResponse validateDeckAccessInput(Long deckId, CollaboratorPermission requiredPermission, String clientIp) {
        // Validate deck ID
        if (deckId == null || deckId <= 0) {
            return DeckAccessValidationResponse.failure("INVALID_DECK_ID", "Invalid deck ID", HttpStatus.BAD_REQUEST);
        }

        // Validate permission
        if (requiredPermission == null) {
            return DeckAccessValidationResponse.failure("INVALID_PERMISSION", "Permission level required", HttpStatus.BAD_REQUEST);
        }

        // Validate IP address
        if (!StringUtils.hasText(clientIp)) {
            log.warn("Client IP address not available for deck access validation");
        } else {
            // Basic IP validation and sanitization
            String sanitizedIp = securityService.sanitizeIpAddress(clientIp);
            if (!sanitizedIp.equals(clientIp)) {
                log.warn("Suspicious IP address detected: {}", clientIp);
                return DeckAccessValidationResponse.builder()
                    .valid(false)
                    .errorCode("SUSPICIOUS_REQUEST")
                    .errorMessage("Request blocked for security reasons")
                    .httpStatus(HttpStatus.FORBIDDEN)
                    .suspiciousActivity(true)
                    .build();
            }
        }

        return DeckAccessValidationResponse.builder().valid(true).build();
    }

    /**
     * Get current user with comprehensive validation
     */
    private User getCurrentUserWithValidation() {
        try {
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return null;
            }

            // Additional user validation
            if (currentUser.getId() == null) {
                log.error("Current user has null ID");
                return null;
            }

            // Check if user account is active (email verified and subscription not expired/cancelled)
            if (!currentUser.isEmailVerified() ||
                currentUser.getSubscriptionStatus() == SubscriptionStatus.EXPIRED ||
                currentUser.getSubscriptionStatus() == SubscriptionStatus.CANCELLED) {
                log.warn("Disabled user attempting deck access: {} (emailVerified: {}, status: {})",
                        currentUser.getId(), currentUser.isEmailVerified(), currentUser.getSubscriptionStatus());
                return null;
            }

            return currentUser;
        } catch (Exception e) {
            log.error("Error getting current user: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Validate subscription requirements for deck access
     */
    private DeckAccessValidationResponse validateSubscriptionForDeckAccess(User user, Deck deck, CollaboratorPermission requiredPermission) {
        SubscriptionStatus status = user.getSubscriptionStatus();

        // Handle null subscription status
        if (status == null) {
            log.warn("User {} has null subscription status", user.getId());
            return DeckAccessValidationResponse.subscriptionRequired();
        }

        switch (status) {
            case EXPIRED:
            case FREE:
                // Per MVP requirements: EXPIRED and FREE users have same restrictions
                return DeckAccessValidationResponse.subscriptionRequired();

            case TRIAL:
                // Trial users have limited access
                if (requiredPermission == CollaboratorPermission.EDIT || requiredPermission == CollaboratorPermission.ADMIN) {
                    // Trial users can only edit their own decks
                    if (!deck.getCreator().getId().equals(user.getId())) {
                        return DeckAccessValidationResponse.builder()
                            .valid(false)
                            .errorCode("TRIAL_LIMITATION")
                            .errorMessage("Trial users can only edit their own decks")
                            .httpStatus(HttpStatus.FORBIDDEN)
                            .trialLimitationApplied(true)
                            .build();
                    }
                }
                break;

            case ACTIVE:
                // Active subscription - full access
                break;

            default:
                log.warn("Unknown subscription status: {} for user: {}", status, user.getId());
                return DeckAccessValidationResponse.subscriptionRequired();
        }

        return DeckAccessValidationResponse.builder().valid(true).build();
    }

    /**
     * Validate security aspects of deck access
     */
    private DeckAccessValidationResponse validateDeckAccessSecurity(User user, Deck deck, String clientIp) {
        try {
            // Check for suspicious patterns
            boolean suspiciousActivity = securityService.detectSuspiciousActivity(user.getId().toString(), clientIp, "deck_access");
            if (suspiciousActivity) {
                log.warn("Suspicious deck access activity detected - User: {}, IP: {}, Deck: {}",
                        user.getId(), clientIp, deck.getId());
                return DeckAccessValidationResponse.builder()
                    .valid(false)
                    .errorCode("SUSPICIOUS_ACTIVITY")
                    .errorMessage("Access blocked due to suspicious activity")
                    .httpStatus(HttpStatus.FORBIDDEN)
                    .suspiciousActivity(true)
                    .requiresAdditionalVerification(true)
                    .build();
            }

            return DeckAccessValidationResponse.builder().valid(true).build();
        } catch (Exception e) {
            log.error("Error during security validation: {}", e.getMessage(), e);
            // Don't block access due to security check failures, but log the issue
            return DeckAccessValidationResponse.builder().valid(true).build();
        }
    }

    /**
     * Get client IP address from request
     */
    private String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }

        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}